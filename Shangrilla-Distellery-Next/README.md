# Shangrila Distillery - Next.js Website

A modern, SEO-optimized website for Shangrila Distillery, Nepal's premier craft spirits producer. Built with Next.js 14, TypeScript, and Tailwind CSS.

## Features

- **SEO Optimized**: Comprehensive meta tags, Open Graph, Twitter Cards, and structured data
- **Age Verification**: Mandatory age verification for alcohol-related content
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Contact Forms**: Multiple contact forms with email integration
- **Blog System**: Dynamic blog posts with SEO optimization
- **Product Showcase**: Interactive product carousel and filtering
- **Leadership Profiles**: Detailed leadership team pages
- **Export Inquiries**: Global partnership inquiry system
- **Modern UI**: Beautiful design with custom distillery branding

## Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Radix UI primitives with shadcn/ui
- **Email**: Nodemailer for contact forms
- **Deployment**: Docker with standalone output

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Docker (for containerized deployment)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd shangrilla-distillery-nextjs
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp env.example .env.local
```

4. Configure your email settings in `.env.local`:
```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

### Development

Run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Production Build

Build the application for production:

```bash
npm run build
```

Start the production server:

```bash
npm start
```

### Docker Deployment

Build and run with Docker:

```bash
docker-compose up --build
```

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   ├── blog/              # Blog pages
│   ├── contact/           # Contact page
│   ├── products/          # Products page
│   └── ...
├── components/            # React components
│   ├── ui/               # Base UI components
│   ├── home/             # Homepage components
│   ├── products/         # Product-related components
│   ├── about/            # About page components
│   └── ...
├── data/                 # Static data (products, blogs)
├── lib/                  # Utility functions
└── ...
```

## SEO Features

- **Dynamic Meta Tags**: Server-side metadata generation
- **Open Graph**: Social media sharing optimization
- **Twitter Cards**: Twitter sharing optimization
- **Structured Data**: JSON-LD for search engines
- **Sitemap**: Automatic sitemap generation
- **Robots.txt**: Search engine crawling instructions

## Contact Forms

The website includes multiple contact forms:

1. **General Contact**: `/contact` - General inquiries
2. **Export Inquiries**: `/exports` - Global partnership inquiries
3. **Sales Inquiries**: `/sales` - Wholesale and retail inquiries

All forms are integrated with Nodemailer for email delivery.

## Customization

### Branding

- Update colors in `tailwind.config.ts`
- Modify fonts in `src/app/globals.css`
- Replace logo images in `public/lovable-uploads/`

### Content

- Update product data in `src/data/products.ts`
- Modify blog posts in `src/data/blogs.ts`
- Edit page content in respective page components

### Styling

- Custom CSS classes in `src/app/globals.css`
- Component-specific styles using Tailwind CSS
- Responsive design with mobile-first approach

## Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Docker

1. Build the Docker image:
```bash
docker build -t shangrila-distillery .
```

2. Run the container:
```bash
docker run -p 3000:3000 shangrila-distillery
```

### Other Platforms

The application can be deployed to any platform that supports Node.js:
- AWS
- Google Cloud Platform
- DigitalOcean
- Heroku

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `EMAIL_HOST` | SMTP server host | Yes |
| `EMAIL_PORT` | SMTP server port | Yes |
| `EMAIL_USER` | SMTP username | Yes |
| `EMAIL_PASS` | SMTP password | Yes |
| `NEXT_PUBLIC_SITE_URL` | Site URL for SEO | No |
| `NEXT_PUBLIC_SITE_NAME` | Site name | No |

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is proprietary to Shangrila Distillery.

## Support

For support or questions, contact:
- Email: <EMAIL>
- Phone: +977-1-4528118