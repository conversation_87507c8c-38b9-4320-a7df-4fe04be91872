import { Metadata } from "next";
import Navigation from "@/components/Navigation";
import Footer from "@/components/home/<USER>";
import BlogCard from "@/components/blog/BlogCard";
import { blogs } from "@/data/blogs";

export const metadata: Metadata = {
  title: "Blog - Shangrila Distillery | Stories, Heritage & Craft Spirits",
  description: "Read our latest blog posts about Shangrila Distillery's heritage, craft spirits, distilling process, and industry insights. Discover the stories behind Nepal's premier distillery.",
  keywords: "Shangrila Distillery Blog, Nepal Spirits Blog, Distillery Stories, Craft Spirits Articles, Whiskey Blog, Vodka Blog, Distilling Process",
  openGraph: {
    title: "Blog - Shangrila Distillery | Stories, Heritage & Craft Spirits",
    description: "Read our latest blog posts about Shangrila Distillery's heritage, craft spirits, distilling process, and industry insights.",
    url: 'https://shangriladistillery.com/blog',
    images: [
      {
        url: '/lovable-uploads/prashanna.png',
        width: 1200,
        height: 630,
        alt: 'Shangrila Distillery Blog',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "Blog - Shangrila Distillery | Stories, Heritage & Craft Spirits",
    description: "Read our latest blog posts about Shangrila Distillery's heritage, craft spirits, distilling process, and industry insights.",
    images: ['/lovable-uploads/prashanna.png'],
  },
  alternates: {
    canonical: 'https://shangriladistillery.com/blog',
  },
};

export default function BlogPage() {
  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-playfair font-bold text-amber-100 mb-6">
              Our Blog
            </h1>
            <p className="text-xl text-amber-100/80 font-crimson max-w-3xl mx-auto leading-relaxed">
              Discover the stories behind our craft, learn about our heritage, and stay updated 
              with the latest from Shangrila Distillery. From distilling techniques to industry insights, 
              our blog offers a glimpse into the world of premium spirits.
            </p>
          </div>

          {/* Blog Posts Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogs.map((blog) => (
              <BlogCard key={blog.id} blog={blog} />
            ))}
          </div>
        </div>

        <Footer />
      </div>
    </div>
  );
}
