import Link from "next/link";
import { ArrowLeft, Home } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Navigation from "@/components/Navigation";
import Footer from "@/components/home/<USER>";

export default function NotFound() {
  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center">
          <div className="distillery-card p-12">
            <div className="mb-8">
              <h1 className="text-6xl font-playfair font-bold text-amber-100 mb-4">404</h1>
              <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-6">
                Blog Post Not Found
              </h2>
              <p className="text-xl distillery-text max-w-2xl mx-auto leading-relaxed font-crimson">
                The blog post you're looking for doesn't exist or may have been moved. 
                Explore our other articles about Shangrila Distillery's heritage, 
                craftsmanship, and premium spirits.
              </p>
            </div>

            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/blog">
                <Button className="premium-button px-6 py-3 font-crimson font-semibold">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Blog
                </Button>
              </Link>
              <Link href="/">
                <Button 
                  variant="outline" 
                  className="border-amber-400/50 text-amber-300 hover:bg-amber-400/10 hover:border-amber-300 px-6 py-3 font-crimson font-semibold"
                >
                  <Home className="mr-2 h-4 w-4" />
                  Go Home
                </Button>
              </Link>
            </div>
          </div>
        </div>

        <Footer />
      </div>
    </div>
  );
}
