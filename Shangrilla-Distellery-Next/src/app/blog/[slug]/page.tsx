import { Metadata } from "next";
import { notFound } from "next/navigation";
import Navigation from "@/components/Navigation";
import Footer from "@/components/home/<USER>";
import { blogs } from "@/data/blogs";
import BlogPost from "@/components/blog/BlogPost";

interface BlogPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: BlogPageProps): Promise<Metadata> {
  const blog = blogs.find((blog) => blog.slug === params.slug);

  if (!blog) {
    return {
      title: "Blog Post Not Found - Shangrila Distillery",
      description: "The requested blog post could not be found.",
    };
  }

  return {
    title: blog.metaTitle,
    description: blog.metaDescription,
    openGraph: {
      title: blog.metaTitle,
      description: blog.metaDescription,
      url: `https://shangriladistillery.com/blog/${blog.slug}`,
      images: blog.image ? [
        {
          url: blog.image,
          width: 1200,
          height: 630,
          alt: blog.title,
        },
      ] : [],
    },
    twitter: {
      card: 'summary_large_image',
      title: blog.metaTitle,
      description: blog.metaDescription,
      images: blog.image ? [blog.image] : [],
    },
    alternates: {
      canonical: `https://shangriladistillery.com/blog/${blog.slug}`,
    },
  };
}

export async function generateStaticParams() {
  return blogs.map((blog) => ({
    slug: blog.slug,
  }));
}

export default function BlogPage({ params }: BlogPageProps) {
  const blog = blogs.find((blog) => blog.slug === params.slug);

  if (!blog) {
    notFound();
  }

  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />

      <div className="pt-20">
        <BlogPost blog={blog} />
        <Footer />
      </div>
    </div>
  );
}
