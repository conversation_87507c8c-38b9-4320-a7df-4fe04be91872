import { Metadata } from "next";
import Navigation from "@/components/Navigation";
import ProductHero from "@/components/products/ProductHero";
import CategoryFilter from "@/components/products/CategoryFilter";
import ProductCard from "@/components/products/ProductCard";
import CraftingProcess from "@/components/products/CraftingProcess";
import AwardsSection from "@/components/products/AwardsSection";
import ComingSoonSection from "@/components/products/ComingSoonSection";
import Footer from "@/components/home/<USER>";
import { products, categories } from "@/data/products";

export const metadata: Metadata = {
  title: "Heritage Collection - Premium Spirits | Shangrila Distillery",
  description: "Explore our complete collection of premium spirits including Tejas Gold, Tejas Black, LOD Vodka, Royal Distinction, and more. Crafted with tradition and excellence in Nepal.",
  keywords: "Shangrila Distillery Products, Nepal Whiskey, Nepal Vodka, Tejas Gold, Tejas Black, LOD Vodka, Royal Distinction, Phantom Whiskey, Lynx Vodka, Reef Vodka, Premium Spirits",
  openGraph: {
    title: "Heritage Collection - Premium Spirits | Shangrila Distillery",
    description: "Explore our complete collection of premium spirits including Tejas Gold, Tejas Black, LOD Vodka, Royal Distinction, and more. Crafted with tradition and excellence in Nepal.",
    url: 'https://shangriladistillery.com/products',
    images: [
      {
        url: '/lovable-uploads/tejas gold.jpeg',
        width: 1200,
        height: 630,
        alt: 'Shangrila Distillery Heritage Collection',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "Heritage Collection - Premium Spirits | Shangrila Distillery",
    description: "Explore our complete collection of premium spirits including Tejas Gold, Tejas Black, LOD Vodka, Royal Distinction, and more.",
    images: ['/lovable-uploads/tejas gold.jpeg'],
  },
  alternates: {
    canonical: 'https://shangriladistillery.com/products',
  },
};

export default function ProductsPage() {
  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <ProductHero />
          
          <CategoryFilter 
            categories={categories}
          />

          {/* Product Banners */}
          <div className="space-y-12 mb-16">
            {products.map((product, index) => (
              <ProductCard key={product.name} product={product} index={index} />
            ))}
          </div>

          <CraftingProcess />
          <AwardsSection />
          <ComingSoonSection />
        </div>

        <Footer />
      </div>
    </div>
  );
}
