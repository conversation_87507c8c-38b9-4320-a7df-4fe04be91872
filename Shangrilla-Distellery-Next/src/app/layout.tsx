import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import  WebsiteWrapper from "@/components/WebsiteWrapper";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Shangrila Distillery - Nepal's Premium Craft Spirits",
  description: "Discover Nepal's finest craft spirits from Shangrila Distillery. Premium whiskey, vodka, and blends crafted with tradition and excellence in Chitwan, Nepal.",
  keywords: "Shangrila Distillery, Nepal Whiskey, Nepal Vodka, Premium Spirits, Craft Distillery, Tejas Gold, Tejas Black, LOD Vodka, Royal Distinction",
  authors: [{ name: "Shangrila Distillery" }],
  creator: "Shangrila Distillery",
  publisher: "Shangrila Distillery",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://shangriladistillery.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "Shangrila Distillery - Nepal's Premium Craft Spirits",
    description: "Discover Nepal's finest craft spirits from Shangrila Distillery. Premium whiskey, vodka, and blends crafted with tradition and excellence.",
    url: 'https://shangriladistillery.com',
    siteName: 'Shangrila Distillery',
    images: [
      {
        url: '/lovable-uploads/fontlogo.jpeg',
        width: 1200,
        height: 630,
        alt: 'Shangrila Distillery - Nepal\'s Premium Craft Spirits',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Shangrila Distillery - Nepal's Premium Craft Spirits",
    description: "Discover Nepal's finest craft spirits from Shangrila Distillery. Premium whiskey, vodka, and blends crafted with tradition and excellence.",
    images: ['/lovable-uploads/fontlogo.jpeg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/lovable-uploads/favicon-shangrila.png" />
        <link rel="apple-touch-icon" href="/lovable-uploads/favicon-shangrila.png" />
        <meta name="theme-color" content="#d4af37" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body className={inter.className}>
        <WebsiteWrapper>
          {children}
        </WebsiteWrapper>
        <Toaster />
      </body>
    </html>
  );
}