import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: NextRequest) {
  try {
    const { companyName, contactPerson, email, country, expectedAnualVolume, distributionExp } = await request.json();

    // Validate required fields
    if (!companyName || !contactPerson || !email || !country || !expectedAnualVolume || !distributionExp) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create transporter
    const transporter = nodemailer.createTransporter({
      host: process.env.EMAIL_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    // Email to team
    const teamEmailHtml = `
      <h2>New Export Partnership Inquiry</h2>
      <p><strong>Company Name:</strong> ${companyName}</p>
      <p><strong>Contact Person:</strong> ${contact<PERSON>erson}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Country:</strong> ${country}</p>
      <p><strong>Expected Annual Volume:</strong> ${expectedAnualVolume}</p>
      <p><strong>Distribution Experience:</strong> ${distributionExp}</p>
    `;

    const confirmationEmailHtml = `
      <h2>Thank You for Your Export Partnership Inquiry!</h2>
      <p>Dear ${companyName},</p>
      <p>We have received your export partnership inquiry and will get back to you soon with detailed information about our distribution opportunities.</p>
      <p>Best regards,<br/>The Shangrila Distillery Export Team</p>
    `;

    // Send email to team
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: process.env.EMAIL_USER,
      subject: `New Export Partnership Inquiry from ${companyName}`,
      html: teamEmailHtml,
    });

    // Send confirmation email to company
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject: "Thank you for your export partnership inquiry!",
      html: confirmationEmailHtml,
    });

    return NextResponse.json(
      { success: true, message: 'Inquiry submitted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Email send error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to submit inquiry' },
      { status: 500 }
    );
  }
}
