import Navigation from "@/components/Navigation";
import Link from "next/link";
import { Facebook, Twitter, Instagram, Linkedin, Phone, Mail, MapPin } from "lucide-react";
import FacilityHero from "@/components/facility/FacilityHero";
import FacilityImageGallery from "@/components/facility/FacilityImageGallery";
import FacilitySpecifications from "@/components/facility/FacilitySpecifications";

import EnvironmentalCommitment from "@/components/facility/EnvironmentalCommitment";
import ExpansionPlans from "@/components/facility/ExpansionPlans";
import Footer from "@/components/home/<USER>";

const Facility = () => {
  return (
    <div className="min-h-screen distillery-gradient">
      <Navigation />
      
      <div className="pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <FacilityHero />
          <FacilityImageGallery />
          <FacilitySpecifications />
          
          <EnvironmentalCommitment />
          <ExpansionPlans />
        </div>

        <Footer />
      </div>
    </div>
  );
};

export default Facility;
