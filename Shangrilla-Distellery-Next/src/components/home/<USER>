import { Award, Globe, Users, Zap } from "lucide-react";

const HeritageBanner = () => {
  const features = [
    {
      icon: Award,
      title: "Award-Winning Quality",
      description: "Recognized for excellence in craft spirits production"
    },
    {
      icon: Globe,
      title: "Global Reach",
      description: "Exporting premium spirits to international markets"
    },
    {
      icon: Users,
      title: "Expert Craftsmanship",
      description: "Master distillers with decades of experience"
    },
    {
      icon: Zap,
      title: "Innovation",
      description: "Blending traditional methods with modern techniques"
    }
  ];

  return (
    <div className="py-16 sm:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-4">
            A Legacy of Excellence
          </h2>
          <p className="text-lg text-amber-100/80 font-crimson max-w-3xl mx-auto">
            For decades, Shangrila Distillery has been crafting exceptional spirits that embody the rich heritage and natural beauty of Nepal.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="distillery-card p-6 text-center group hover:scale-105 transition-transform duration-300">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-amber-500/20 rounded-full mb-4 group-hover:bg-amber-500/30 transition-colors">
                <feature.icon className="h-8 w-8 text-amber-400" />
              </div>
              <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">
                {feature.title}
              </h3>
              <p className="distillery-text text-sm">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default HeritageBanner;
