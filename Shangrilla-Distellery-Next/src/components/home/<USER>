"use client";

import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { products } from "@/data/products";

const ProductCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === products.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? products.length - 1 : prevIndex - 1
    );
  };

  // Auto-advance carousel
  useEffect(() => {
    const interval = setInterval(nextSlide, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="py-16 sm:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-4">
            Our Heritage Collection
          </h2>
          <p className="text-lg text-amber-100/80 font-crimson max-w-3xl mx-auto">
            Discover our carefully curated selection of premium spirits, each crafted with precision and passion.
          </p>
        </div>

        <div className="relative">
          <div className="overflow-hidden rounded-2xl">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentIndex * 100}%)` }}
            >
              {products.map((product, index) => (
                <div key={product.name} className="w-full flex-shrink-0">
                  <div className={`bg-gradient-to-br ${product.bgColor} p-8 sm:p-12 lg:p-16 rounded-2xl`}>
                    <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
                      <div className="text-center lg:text-left">
                        <h3 className="text-2xl sm:text-3xl lg:text-4xl font-playfair font-bold text-white mb-4">
                          {product.name}
                        </h3>
                        <p className="text-lg text-white/90 mb-6 font-crimson leading-relaxed">
                          {product.description.split('\n')[0]}
                        </p>
                        <div className="space-y-2 mb-6">
                          <p className="text-white/80 font-semibold">ABV: {product.alcohol}</p>
                          <p className="text-white/80">Type: {product.type}</p>
                        </div>
                        <Button className="bg-white/20 hover:bg-white/30 text-white border border-white/30 backdrop-blur-sm">
                          Learn More
                        </Button>
                      </div>
                      <div className="flex justify-center">
                        <img
                          src={product.image}
                          alt={product.name}
                          className="h-64 sm:h-80 lg:h-96 w-auto object-contain drop-shadow-2xl"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation buttons */}
          <Button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white border border-white/30 backdrop-blur-sm"
            size="icon"
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>
          <Button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white border border-white/30 backdrop-blur-sm"
            size="icon"
          >
            <ChevronRight className="h-6 w-6" />
          </Button>
        </div>

        {/* Dots indicator */}
        <div className="flex justify-center mt-8 space-x-2">
          {products.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex 
                  ? 'bg-amber-400 w-8' 
                  : 'bg-amber-400/30 hover:bg-amber-400/50'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProductCarousel;
