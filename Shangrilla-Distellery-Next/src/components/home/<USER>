import Link from "next/link";
import { ArrowRight, Calendar } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { blogs } from "@/data/blogs";

const FeaturedBlog = () => {
  const featuredBlog = blogs[0]; // Get the first blog post

  return (
    <div className="py-16 sm:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-4">
            Latest from Our Blog
          </h2>
          <p className="text-lg text-amber-100/80 font-crimson max-w-3xl mx-auto">
            Discover the stories behind our craft, learn about our heritage, and stay updated with the latest from Shangrila Distillery.
          </p>
        </div>

        <div className="distillery-card p-8 lg:p-12">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div>
              <div className="flex items-center mb-4">
                <Calendar className="h-5 w-5 text-amber-400 mr-2" />
                <span className="text-amber-300 text-sm font-crimson">Featured Article</span>
              </div>
              <h3 className="text-2xl sm:text-3xl lg:text-4xl font-playfair font-bold text-amber-100 mb-4">
                {featuredBlog.title}
              </h3>
              <p className="text-lg text-amber-100/80 font-crimson mb-6 leading-relaxed">
                {featuredBlog.introduction}
              </p>
              <Link href={`/blog/${featuredBlog.slug}`}>
                <Button className="premium-button px-6 py-3 text-base font-baskerville tracking-wide">
                  Read Full Article
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
            <div className="flex justify-center">
              <img
                src={featuredBlog.image}
                alt={featuredBlog.title}
                className="h-64 sm:h-80 lg:h-96 w-auto object-cover rounded-lg shadow-2xl"
              />
            </div>
          </div>
        </div>

        <div className="text-center mt-12">
          <Link href="/blog">
            <Button variant="outline" className="border-2 border-amber-400/70 text-amber-300 hover:bg-amber-400/10 hover:border-amber-300 px-8 py-4 text-lg font-baskerville backdrop-blur-sm">
              View All Articles
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default FeaturedBlog;
