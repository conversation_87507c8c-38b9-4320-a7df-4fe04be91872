import Link from "next/link";
import { ExternalLink, Globe, Users } from "lucide-react";

const ExportPartner = () => {
  const partners = [
    {
      name: "Trade Vision Partners",
      location: "Australia",
      description: "Our flagship partnership brings decades of international distribution expertise, opening doors to premium markets across Australia and beyond.",
      website: "https://tradevisionpartners.com/",
      icon: Globe
    },
    {
      name: "ShyamBaba Group",
      location: "Nepal",
      description: "Nepal's foremost diversified business group with interests in Food Products, Cement, Construction, Manufacturing and Trading. With over 2000 employees and 10,000 direct customers.",
      website: "https://sbgcompanies.com/",
      icon: Users
    }
  ];

  return (
    <div className="py-16 sm:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-4">
            Strategic Partnerships
          </h2>
          <p className="text-lg text-amber-100/80 font-crimson max-w-3xl mx-auto">
            Building bridges across continents through strategic partnerships that amplify our global reach and impact.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {partners.map((partner, index) => (
            <div key={index} className="distillery-card p-8 group hover:scale-105 transition-transform duration-300">
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-amber-500/20 rounded-full mr-4">
                    <partner.icon className="h-6 w-6 text-amber-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-playfair font-bold text-amber-200 mb-1">
                      {partner.name}
                    </h3>
                    <p className="text-amber-300 text-sm">{partner.location}</p>
                  </div>
                </div>
                <Link 
                  href={partner.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-amber-400 hover:text-amber-300 transition-colors"
                >
                  <ExternalLink className="h-5 w-5" />
                </Link>
              </div>
              <p className="distillery-text text-base leading-relaxed">
                {partner.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ExportPartner;
