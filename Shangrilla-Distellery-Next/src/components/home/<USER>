const LegacyStats = () => {
  const stats = [
    {
      number: "25+",
      label: "Years of Excellence",
      description: "Crafting premium spirits since 1998"
    },
    {
      number: "7",
      label: "Premium Varieties",
      description: "Whiskey, vodka, and specialty blends"
    },
    {
      number: "50+",
      label: "Countries Served",
      description: "Global distribution network"
    },
    {
      number: "100K+",
      label: "Bottles Produced",
      description: "Annual production capacity"
    }
  ];

  return (
    <div className="py-16 sm:py-20 bg-gradient-to-r from-amber-900/30 to-orange-900/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-4">
            Our Legacy in Numbers
          </h2>
          <p className="text-lg text-amber-100/80 font-crimson max-w-3xl mx-auto">
            Decades of dedication, innovation, and uncompromising quality have made us Nepal's premier distillery.
          </p>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="distillery-card p-6 group-hover:scale-105 transition-transform duration-300">
                <div className="text-4xl sm:text-5xl lg:text-6xl font-playfair font-bold text-amber-400 mb-2">
                  {stat.number}
                </div>
                <h3 className="text-lg font-playfair font-semibold text-amber-100 mb-2">
                  {stat.label}
                </h3>
                <p className="distillery-text text-sm">
                  {stat.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LegacyStats;
