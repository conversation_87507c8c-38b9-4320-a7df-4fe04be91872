import Link from "next/link";
import { ArrowR<PERSON>, Phone, Mail } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const CallToAction = () => {
  return (
    <div className="py-16 sm:py-20 bg-gradient-to-r from-amber-600 to-amber-500">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-playfair font-bold text-stone-900 mb-6">
            Ready to Experience Excellence?
          </h2>
          <p className="text-lg text-stone-800 mb-8 font-crimson max-w-3xl mx-auto">
            Join us in celebrating the art of fine spirits. Whether you're a connoisseur or new to our collection, 
            we invite you to discover what makes Shangrila Distillery truly special.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
            <Link href="/contact">
              <Button className="bg-stone-900 hover:bg-stone-800 text-amber-100 px-8 py-4 text-lg font-baskerville tracking-wide">
                <Mail className="mr-2 h-5 w-5" />
                Get in Touch
              </Button>
            </Link>
            <Link href="/products">
              <Button variant="outline" className="border-2 border-stone-900 text-stone-900 hover:bg-stone-900 hover:text-amber-100 px-8 py-4 text-lg font-baskerville">
                Explore Collection
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center text-stone-800">
            <div className="flex items-center">
              <Phone className="h-5 w-5 mr-2" />
              <span className="font-semibold">+977-1-4528118</span>
            </div>
            <div className="flex items-center">
              <Mail className="h-5 w-5 mr-2" />
              <span className="font-semibold"><EMAIL></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallToAction;
