import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { products } from "@/data/products";

const FlagshipCollection = () => {
  const featuredProducts = products.slice(0, 4);

  return (
    <div className="py-16 sm:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-4">
            Flagship Collection
          </h2>
          <p className="text-lg text-amber-100/80 font-crimson max-w-3xl mx-auto">
            Our signature spirits that define the Shangrila experience - each bottle tells a story of craftsmanship and tradition.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {featuredProducts.map((product) => (
            <div key={product.name} className="group">
              <div className="distillery-card p-6 group-hover:scale-105 transition-transform duration-300">
                <div className="aspect-square mb-4 flex items-center justify-center">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="h-32 w-auto object-contain drop-shadow-lg"
                  />
                </div>
                <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">
                  {product.name}
                </h3>
                <p className="text-sm text-amber-100/70 mb-3 font-crimson">
                  {product.type}
                </p>
                <p className="text-sm text-amber-100/60 mb-4">
                  ABV: {product.alcohol}
                </p>
                <div className="space-y-1">
                  {product.notes.slice(0, 2).map((note, noteIndex) => (
                    <p key={noteIndex} className="text-xs text-amber-100/60">
                      • {note}
                    </p>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link href="/products">
            <Button className="premium-button px-8 py-4 text-lg font-baskerville tracking-wide">
              View All Products
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default FlagshipCollection;
