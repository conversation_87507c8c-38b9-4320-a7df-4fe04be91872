import Link from "next/link";
import { ArrowLeft, Calendar, Clock, Share2 } from "lucide-react";
import { Button } from "@/components/ui/button";

interface BlogSection {
  title: string;
  content: string;
}

interface Blog {
  id: number;
  slug: string;
  image?: string;
  metaTitle: string;
  metaDescription: string;
  title: string;
  introduction: string;
  sections: BlogSection[];
  conclusion: string;
}

interface BlogPostProps {
  blog: Blog;
}

const BlogPost = ({ blog }: BlogPostProps) => {
  const formatContent = (content: string) => {
    // Split content by double newlines to create paragraphs
    const paragraphs = content.split('\n\n');
    
    return paragraphs.map((paragraph, index) => {
      // Check if it's a list item (starts with bullet point or dash)
      if (paragraph.includes('\n') && (paragraph.includes('–') || paragraph.includes('-'))) {
        const items = paragraph.split('\n').filter(item => item.trim());
        return (
          <ul key={index} className="space-y-2 mb-6">
            {items.map((item, itemIndex) => (
              <li key={itemIndex} className="text-lg distillery-text leading-relaxed font-crimson flex items-start">
                <span className="text-amber-400 mr-3 mt-1">•</span>
                <span>{item.replace(/^[–-]\s*/, '')}</span>
              </li>
            ))}
          </ul>
        );
      }
      
      return (
        <p key={index} className="text-lg distillery-text leading-relaxed font-crimson mb-6">
          {paragraph}
        </p>
      );
    });
  };

  return (
    <article className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Back to Blog Link */}
      <div className="mb-8">
        <Link href="/blog" className="inline-flex items-center text-amber-400 hover:text-amber-300 transition-colors font-crimson">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Blog
        </Link>
      </div>

      {/* Hero Image */}
      {blog.image && (
        <div className="mb-12">
          <div className="relative overflow-hidden rounded-2xl">
            <img
              src={blog.image}
              alt={blog.title}
              className="w-full h-64 sm:h-80 lg:h-96 object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
          </div>
        </div>
      )}

      {/* Article Header */}
      <header className="mb-12">
        <h1 className="text-3xl sm:text-4xl lg:text-5xl font-playfair font-bold text-amber-100 mb-6 leading-tight">
          {blog.title}
        </h1>
        
        <div className="flex flex-wrap items-center gap-6 text-amber-300 mb-8">
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-2" />
            <span className="text-sm font-crimson">Featured Article</span>
          </div>
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            <span className="text-sm font-crimson">5 min read</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            className="border-amber-400/50 text-amber-300 hover:bg-amber-400/10 hover:border-amber-300"
          >
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
        </div>

        <div className="distillery-card p-6 mb-8">
          <p className="text-xl distillery-text leading-relaxed font-crimson">
            {blog.introduction}
          </p>
        </div>
      </header>

      {/* Article Content */}
      <div className="prose prose-lg max-w-none">
        {blog.sections.map((section, index) => (
          <section key={index} className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-playfair font-bold text-amber-100 mb-6">
              {section.title}
            </h2>
            <div className="distillery-card p-6">
              {formatContent(section.content)}
            </div>
          </section>
        ))}

        {/* Conclusion */}
        {blog.conclusion && (
          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-playfair font-bold text-amber-100 mb-6">
              Conclusion
            </h2>
            <div className="distillery-card p-6 border-l-4 border-amber-400">
              {formatContent(blog.conclusion)}
            </div>
          </section>
        )}
      </div>

      {/* Call to Action */}
      <div className="distillery-card p-8 text-center mt-16">
        <h3 className="text-2xl font-playfair font-bold text-amber-100 mb-4">
          Discover More About Shangrila Distillery
        </h3>
        <p className="text-lg distillery-text mb-6 max-w-2xl mx-auto">
          Explore our premium spirits, learn about our heritage, or visit our facility in Chitwan.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Link href="/products">
            <Button className="premium-button px-6 py-3 font-crimson font-semibold">
              View Our Products
            </Button>
          </Link>
          <Link href="/facility">
            <Button 
              variant="outline" 
              className="border-amber-400/50 text-amber-300 hover:bg-amber-400/10 hover:border-amber-300 px-6 py-3 font-crimson font-semibold"
            >
              Visit Our Facility
            </Button>
          </Link>
        </div>
      </div>
    </article>
  );
};

export default BlogPost;
