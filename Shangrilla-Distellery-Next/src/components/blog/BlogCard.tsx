import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

interface BlogCardProps {
  slug: string;
  title: string;
  introduction: string;
}

const BlogCard = ({ slug, title, introduction }: BlogCardProps) => {
  return (
    <div className="distillery-card p-6 hover:scale-105 transition-all duration-300 flex flex-col">
      <h3 className="text-xl font-playfair font-bold text-amber-100 mb-4">{title}</h3>
      <p className="distillery-text mb-6 flex-grow">{introduction}</p>
      <Link href={`/blog/${slug}`}>
        <Button className="w-full premium-button font-crimson font-semibold">
          Read More
        </Button>
      </Link>
    </div>
  );
};

export default BlogCard;
