import Link from "next/link";
import { Calendar, Clock, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface BlogSection {
  title: string;
  content: string;
}

interface Blog {
  id: number;
  slug: string;
  image?: string;
  metaTitle: string;
  metaDescription: string;
  title: string;
  introduction: string;
  sections: BlogSection[];
  conclusion: string;
}

interface BlogCardProps {
  blog: Blog;
}

const BlogCard = ({ blog }: BlogCardProps) => {
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + "...";
  };

  return (
    <article className="distillery-card overflow-hidden hover:scale-105 transition-all duration-300 flex flex-col group">
      {/* Image */}
      {blog.image && (
        <div className="relative overflow-hidden h-48">
          <img
            src={blog.image}
            alt={blog.title}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
        </div>
      )}

      <div className="p-6 flex flex-col flex-grow">
        {/* Meta Info */}
        <div className="flex items-center gap-4 text-amber-300 text-sm mb-4">
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-1" />
            <span className="font-crimson">Featured</span>
          </div>
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-1" />
            <span className="font-crimson">5 min read</span>
          </div>
        </div>

        {/* Title */}
        <h3 className="text-xl font-playfair font-bold text-amber-100 mb-4 leading-tight group-hover:text-amber-200 transition-colors">
          {blog.title}
        </h3>

        {/* Introduction */}
        <p className="distillery-text mb-6 flex-grow leading-relaxed font-crimson">
          {truncateText(blog.introduction, 150)}
        </p>

        {/* Read More Button */}
        <Link href={`/blog/${blog.slug}`} className="mt-auto">
          <Button className="w-full premium-button font-crimson font-semibold group-hover:scale-105 transition-transform">
            Read Full Article
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </Link>
      </div>
    </article>
  );
};

export default BlogCard;
