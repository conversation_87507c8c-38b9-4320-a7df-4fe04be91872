"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { Menu, X } from "lucide-react";
import Image from "next/image";

const Navigation = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  const navItems = [
    { path: "/", label: "Home" },
    { path: "/about", label: "Our Story" },
    { path: "/products", label: "Heritage Collection" },
    { path: "/facility", label: "Master Distillery" },
    { path: "/sales", label: "Sales" },
    { path: "/exports", label: "Exports" },
    { path: "/events", label: "Events & Notices" },
    { path: "/blog", label: "Blog" },
    { path: "/careers", label: "Careers" },
    { path: "/our-leadership", label: "Our Leadership" },
    { path: "/contact", label: "Visit Us" },
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 bg-stone-900/95 backdrop-blur-md border-b border-amber-400/30 z-50 shadow-lg shadow-amber-500/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 group">
            <div className="relative flex items-center justify-center h-16 w-16 bg-stone-900 overflow-hidden rounded-full">
              <Image 
                src="/lovable-uploads/favicon-shangrila.png" 
                alt="Shangrila Distillery" 
                className="h-16 w-16 object-cover filter brightness-125 contrast-125 drop-shadow-lg transition-all duration-500 rounded-full"
                width={64}
                height={64}
              />
            </div>
          </Link>
          
          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.path}
                href={item.path}
                className={`font-crimson font-medium text-sm transition-all duration-300 relative group ${
                  pathname === item.path
                    ? "text-amber-300"
                    : "text-amber-100/80 hover:text-amber-300"
                }`}
              >
                {item.label}
                <span className={`absolute -bottom-1 left-0 h-0.5 bg-amber-400 transition-all duration-300 ${
                  pathname === item.path ? "w-full" : "w-0 group-hover:w-full"
                }`}></span>
              </Link>
            ))}
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-amber-100 hover:text-amber-300 transition-colors p-2"
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="lg:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-stone-900/98 backdrop-blur-md border-t border-amber-400/30 rounded-b-lg">
              {navItems.map((item) => (
                <Link
                  key={item.path}
                  href={item.path}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className={`block px-4 py-3 font-crimson font-medium transition-all duration-300 rounded-md ${
                    pathname === item.path
                      ? "text-amber-300 bg-amber-400/10 border-l-4 border-amber-400"
                      : "text-amber-100/80 hover:text-amber-300 hover:bg-amber-400/5"
                  }`}
                >
                  {item.label}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
