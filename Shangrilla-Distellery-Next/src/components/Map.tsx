"use client";

import { useEffect, useRef } from "react";

const Map = () => {
  const mapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Simple map placeholder - in a real implementation, you would integrate with Google Maps or Mapbox
    if (mapRef.current) {
      mapRef.current.innerHTML = `
        <div style="
          width: 100%;
          height: 300px;
          background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #d4af37;
          font-family: 'Crimson Text', serif;
          font-size: 18px;
          text-align: center;
          border: 2px solid #d4af37;
        ">
          <div>
            <div style="font-size: 24px; margin-bottom: 8px;">📍</div>
            <div>Interactive Map</div>
            <div style="font-size: 14px; margin-top: 4px; opacity: 0.8;">
              Kathmandu & Chitwan Locations
            </div>
          </div>
        </div>
      `;
    }
  }, []);

  return (
    <div className="distillery-card p-6">
      <h3 className="text-lg font-playfair font-semibold text-amber-100 mb-4">
        Find Us
      </h3>
      <div ref={mapRef} className="w-full h-64 rounded-lg overflow-hidden"></div>
      <div className="mt-4 text-sm text-amber-100/70">
        <p>📍 Head Office: Pipalbot dillibazar-29, Kathmandu</p>
        <p>🏭 Factory: Brahmanagar, Rapti Nagarpalika-9, Chitwan</p>
      </div>
    </div>
  );
};

export default Map;
