import { Award, Star, Trophy } from "lucide-react";

const AwardsSection = () => {
  const awards = [
    {
      icon: Trophy,
      title: "International Recognition",
      description: "Awarded for excellence in craft spirits production at international competitions",
      year: "2023"
    },
    {
      icon: Star,
      title: "Quality Excellence",
      description: "Recognized for maintaining the highest standards in distillation and blending",
      year: "2022"
    },
    {
      icon: Award,
      title: "Innovation Award",
      description: "Honored for innovative approaches to traditional distilling methods",
      year: "2021"
    }
  ];

  return (
    <div className="py-16 sm:py-20 bg-gradient-to-r from-amber-900/30 to-orange-900/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-playfair font-bold text-amber-100 mb-4">
            Recognition & Awards
          </h2>
          <p className="text-lg text-amber-100/80 font-crimson max-w-3xl mx-auto">
            Our commitment to excellence has been recognized by industry experts and international competitions, 
            validating our position as Nepal's premier distillery.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {awards.map((award, index) => (
            <div key={index} className="text-center group">
              <div className="distillery-card p-8 group-hover:scale-105 transition-transform duration-300">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-amber-500/20 rounded-full mb-6 group-hover:bg-amber-500/30 transition-colors">
                  <award.icon className="h-10 w-10 text-amber-400" />
                </div>
                <div className="text-2xl font-playfair font-bold text-amber-400 mb-2">
                  {award.year}
                </div>
                <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-4">
                  {award.title}
                </h3>
                <p className="distillery-text text-sm leading-relaxed">
                  {award.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AwardsSection;
