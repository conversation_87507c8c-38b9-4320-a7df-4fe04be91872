"use client";

import { Filter } from "lucide-react";
import { Button } from "@/components/ui/button";

interface CategoryFilterProps {
  categories: string[];
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
}

const CategoryFilter = ({ categories, selectedCategory, onCategoryChange }: CategoryFilterProps) => {
  return (
    <div className="flex items-center justify-center mb-16">
      <div className="flex items-center space-x-2 distillery-card p-3 rounded-xl">
        <Filter className="h-5 w-5 text-amber-400" />
        {categories.map((category) => (
          <Button
            key={category}
            onClick={() => onCategoryChange(category)}
            variant={selectedCategory === category ? "default" : "ghost"}
            className={selectedCategory === category 
              ? "premium-button font-crimson" 
              : "text-amber-100 hover:bg-amber-400/20 font-crimson"
            }
          >
            {category}
          </Button>
        ))}
      </div>
    </div>
  );
};

export default CategoryFilter;
