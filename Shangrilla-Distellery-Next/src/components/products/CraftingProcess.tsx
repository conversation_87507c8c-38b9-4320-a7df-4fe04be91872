
import { <PERSON>rape, Zap, Star } from "lucide-react";

const CraftingProcess = () => {
  const craftingProcess = [
    {
      icon: Grape,
      title: "Premium Ingredients",
      description: "Sourced from the finest local and international suppliers"
    },
    {
      icon: Zap,
      title: "Modern Distillation",
      description: "State-of-the-art equipment ensuring consistency and purity"
    },
    {
      icon: Star,
      title: "Master Blending",
      description: "Expert craftsmanship in every bottle"
    }
  ];

  return (
    <div className="mb-16">
      <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-12 text-center">Our Heritage Crafting Process</h2>
      <div className="grid md:grid-cols-3 gap-8">
        {craftingProcess.map((process) => (
          <div key={process.title} className="distillery-card p-8 text-center hover:scale-105 transition-all duration-300">
            <div className="w-16 h-16 bg-amber-400/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <process.icon className="h-8 w-8 text-amber-400" />
            </div>
            <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-4">{process.title}</h3>
            <p className="distillery-text font-crimson">{process.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CraftingProcess;
