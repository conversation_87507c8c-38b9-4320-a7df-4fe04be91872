"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface AgeVerificationProps {
  onVerified: () => void;
}

const AgeVerification = ({ onVerified }: AgeVerificationProps) => {
  const [birthDate, setBirthDate] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!birthDate) {
      setError("Please enter your date of birth");
      return;
    }

    // Parse DD/MM/YYYY
    const [day, month, year] = birthDate.split("/").map(Number);
    if (!day || !month || !year || year < 1900) {
      setError("Please enter a valid date in DD/MM/YYYY format");
      return;
    }
    // JS months are 0-based
    const birth = new Date(year, month - 1, day);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    if (age >= 18) {
      localStorage.setItem("ageVerified", "true");
      onVerified();
    } else {
      setError("You must be 18 years or older to access this website");
    }
  };

  const handleBirthDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/[^0-9]/g, ""); // Only digits
    if (value.length > 8) value = value.slice(0, 8);
    let formatted = value;
    if (value.length > 4) {
      formatted = value.slice(0, 2) + "/" + value.slice(2, 4) + "/" + value.slice(4);
    } else if (value.length > 2) {
      formatted = value.slice(0, 2) + "/" + value.slice(2);
    }
    setBirthDate(formatted);
  };

  return (
    <div className="min-h-screen distillery-gradient flex items-center justify-center p-4 relative overflow-hidden">
      <div className="absolute inset-0 bg-distillery-texture opacity-20"></div>
      <div className="distillery-card p-8 max-w-md w-full relative z-10 border-2 border-amber-500/30">
        <div className="text-center mb-8">
          <img 
            src="/lovable-uploads/favicon-shangrila.png" 
            alt="Shangrila Distillery" 
            className="h-24 w-auto mx-auto mb-6 filter brightness-110"
          />
          <h1 className="text-3xl font-playfair font-bold text-amber-100 mb-4">Shangrila Distillery</h1>
          <p className="text-amber-100/80 font-crimson leading-relaxed">
            You must be of legal drinking age to enter the website for the best distillery in Kathmandu.
          </p>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <Label htmlFor="birthDate" className="text-amber-200 font-baskerville text-lg mb-2 block">
              Date of Birth
            </Label>
            <Input
              id="birthDate"
              type="text"
              value={birthDate}
              onChange={handleBirthDateChange}
              placeholder="DD / MM / YYYY"
              className="bg-stone-900/50 border-amber-500/50 text-amber-100 placeholder-amber-100/50 focus:border-amber-400 focus:ring-amber-400/30 backdrop-blur-sm"
            />
          </div>
          
          {error && (
            <div className="text-red-400 text-sm font-crimson bg-red-950/30 border border-red-500/30 rounded-lg p-3 backdrop-blur-sm">
              {error}
            </div>
          )}
          
          <Button 
            type="submit" 
            className="w-full premium-button py-4 text-lg font-baskerville tracking-wide"
          >
            Enter Website
          </Button>
        </form>
        
        <div className="mt-6 text-xs text-amber-100/60 text-center font-crimson leading-relaxed">
          By entering this website, you confirm that you are of legal drinking age in your jurisdiction.
        </div>
      </div>
    </div>
  );
};

export default AgeVerification;
