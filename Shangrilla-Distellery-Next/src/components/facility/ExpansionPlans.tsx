import { Building, Zap, Globe, Target } from "lucide-react";

const ExpansionPlans = () => {
  const expansionPlans = [
    {
      icon: Building,
      title: "Storage Capacity Expansion",
      description: "2,00,000+ litres storage capacity with climate-controlled facilities for optimal aging and inventory management.",
      timeline: "Current & Expanding"
    },
    {
      icon: Globe,
      title: "Phase 1: Oceania Markets",
      description: "Export to Oceania Markets & achieve significant market share in the domestic market with our premium spirits.",
      timeline: "2024-2025"
    },
    {
      icon: Target,
      title: "Phase 2: Global Expansion",
      description: "Expand to more continents and introduce more premium and different products to diversify our portfolio.",
      timeline: "2025-2027"
    },
    {
      icon: Zap,
      title: "Technology Upgrade",
      description: "Implementing advanced automation and quality monitoring systems for enhanced precision and consistency.",
      timeline: "2025"
    }
  ];

  return (
    <div className="mb-20">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-playfair font-bold text-amber-100 mb-6">Future Expansion Plans</h2>
        <p className="text-xl distillery-text max-w-3xl mx-auto">
          Our vision extends beyond today's achievements. We're committed to continuous growth
          and innovation while maintaining the highest standards of quality and craftsmanship.
        </p>
      </div>

      {/* Storage Capacity Highlight */}
      <div className="distillery-card p-8 mb-16 text-center">
        <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-4">2,00,000+ litres storage capacity</h2>
        <p className="text-lg distillery-text max-w-2xl mx-auto">
          and planning on more
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8">
        {expansionPlans.map((plan) => (
          <div key={plan.title} className="distillery-card p-8 hover:scale-105 transition-all duration-300">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-amber-400/20 rounded-full flex items-center justify-center flex-shrink-0">
                <plan.icon className="h-6 w-6 text-amber-400" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-xl font-playfair font-semibold text-amber-100">{plan.title}</h3>
                  <span className="text-sm text-amber-400 font-crimson font-semibold">{plan.timeline}</span>
                </div>
                <p className="distillery-text font-crimson leading-relaxed">{plan.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-12 text-center">
        <div className="distillery-card p-8 max-w-4xl mx-auto">
          <h3 className="text-2xl font-playfair font-bold text-amber-100 mb-4">Our Commitment to Excellence</h3>
          <p className="distillery-text font-crimson text-lg leading-relaxed">
            Every expansion plan is designed with sustainability, quality, and innovation at its core.
            We're not just growing our capacity – we're elevating the entire industry standard for
            premium spirit production in Nepal and beyond.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ExpansionPlans;