"use client";

import { useState, useEffect } from "react";
import AgeVerification from "./AgeVerification";

interface WebsiteWrapperProps {
  children: React.ReactNode;
}

const WebsiteWrapper = ({ children }: WebsiteWrapperProps) => {
  const [isVerified, setIsVerified] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const verified = localStorage.getItem("ageVerified");
    if (verified === "true") {
      setIsVerified(true);
    }
    setIsLoading(false);
  }, []);

  const handleVerification = () => {
    setIsVerified(true);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 flex items-center justify-center">
        <div className="text-amber-700">Loading...</div>
      </div>
    );
  }

  if (!isVerified) {
    return <AgeVerification onVerified={handleVerification} />;
  }

  return <>{children}</>;
};

export default WebsiteWrapper;
