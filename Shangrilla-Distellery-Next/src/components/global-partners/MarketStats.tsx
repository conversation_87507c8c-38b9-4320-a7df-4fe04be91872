
import { <PERSON><PERSON><PERSON>, MapPin, Users } from "lucide-react";

const MarketStats = () => {
  const marketStats = [
    {
      icon: Bar<PERSON><PERSON>,
      title: "Export Growth",
      value: "300%",
      description: "Projected growth over 3 years"
    },
    {
      icon: MapPin,
      title: "Target Markets",
      value: "15+",
      description: "Countries in expansion plan"
    },
    {
      icon: Users,
      title: "Partner Network",
      value: "25+",
      description: "Potential distribution partners"
    }
  ];

  return (
    <div className="grid md:grid-cols-3 gap-8 mb-16">
      {marketStats.map((stat) => (
        <div key={stat.title} className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
          <div className="w-16 h-16 bg-amber-400/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <stat.icon className="h-8 w-8 text-amber-400" />
          </div>
          <div className="text-3xl font-playfair font-bold text-amber-400 mb-2">{stat.value}</div>
          <h3 className="text-lg font-playfair font-semibold text-amber-100 mb-1">{stat.title}</h3>
          <p className="distillery-text text-sm">{stat.description}</p>
        </div>
      ))}
    </div>
  );
};

export default MarketStats;
