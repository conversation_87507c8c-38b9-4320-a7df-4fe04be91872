import { Handshake, ExternalLink } from "lucide-react";

const StrategicPartnerships = () => {
  return (
    <div className="mb-16">
      <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-8 text-center">Strategic Partnerships</h2>
      <div className="grid md:grid-cols-2 gap-8">
        {/* Trade Vision Partners */}
        <div className="distillery-card p-8 text-center">
          <Handshake className="h-16 w-16 mx-auto mb-6 text-amber-400 animate-float" />
          <div className="flex items-center justify-center mb-4">
            <h3 className="text-2xl font-playfair font-bold text-amber-100 mr-3">Trade Vision Partners</h3>
            <a 
              href="https://tradevisionpartners.com/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-amber-400 hover:text-amber-300 transition-colors"
            >
              <ExternalLink className="h-6 w-6" />
            </a>
          </div>
          <p className="text-amber-300 mb-4 font-crimson">Australia</p>
          <p className="text-lg distillery-text">
            Our flagship partnership brings decades of international distribution expertise, 
            opening doors to premium markets across Australia and beyond. Together, we're 
            setting new standards for Nepalese spirits on the global stage.
          </p>
        </div>

        {/* SBG Companies */}
        <div className="distillery-card p-8 text-center">
          <Handshake className="h-16 w-16 mx-auto mb-6 text-amber-400 animate-float" />
          <div className="flex items-center justify-center mb-4">
            <h3 className="text-2xl font-playfair font-bold text-amber-100 mr-3">ShyamBaba Group</h3>
            <a 
              href="https://sbgcompanies.com/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-amber-400 hover:text-amber-300 transition-colors"
            >
              <ExternalLink className="h-6 w-6" />
            </a>
          </div>
          <p className="text-amber-300 mb-4 font-crimson">Nepal</p>
          <p className="text-lg distillery-text mb-4">
            Nepal's foremost diversified business group with interests in Food Products, 
            Cement, Construction, Manufacturing and Trading.
          </p>
          <p className="distillery-text">
            With over 2000 employees and 10,000 direct customers, ShyamBaba Group touches 
            lives across Nepal through its vast offering of market-leading high quality 
            consumer products.
          </p>
        </div>
      </div>
    </div>
  );
};

export default StrategicPartnerships;
