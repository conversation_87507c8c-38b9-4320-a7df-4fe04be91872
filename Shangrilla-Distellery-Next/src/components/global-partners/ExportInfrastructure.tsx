
import { Award, Truck, Globe, Users } from "lucide-react";

const ExportInfrastructure = () => {
  const exportCapabilities = [
    {
      icon: Award,
      title: "International Standards",
      description: "ISO certified facilities meeting global quality requirements"
    },
    {
      icon: Truck,
      title: "Logistics Excellence", 
      description: "Streamlined export processes and cold chain management"
    },
    {
      icon: Globe,
      title: "Global Compliance",
      description: "Meeting regulatory requirements across target markets"
    },
    {
      icon: Users,
      title: "Partner Support",
      description: "Dedicated team for international business development"
    }
  ];

  return (
    <div className="mb-16">
      <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-8 text-center">Export-Ready Infrastructure</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
        {exportCapabilities.map((capability) => (
          <div key={capability.title} className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
            <capability.icon className="h-12 w-12 text-amber-400 mx-auto mb-4" />
            <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">{capability.title}</h3>
            <p className="distillery-text">{capability.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ExportInfrastructure;
