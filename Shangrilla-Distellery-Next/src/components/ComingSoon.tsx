
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

interface ComingSoonProps {
  title: string;
  description?: string;
}

const ComingSoon = ({ title, description }: ComingSoonProps) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-b border-amber-200 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2">
              <img 
                src="/lovable-uploads/cfc50220-4059-4960-a0ea-0b328b8473e8.png" 
                alt="Shangrila Distillery" 
                className="h-16 w-auto"
              />
            </Link>
            
            {/* Navigation Links */}
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/" className="text-amber-800 hover:text-amber-600 font-medium transition-colors">
                Home
              </Link>
              <Link href="/about" className="text-amber-800 hover:text-amber-600 font-medium transition-colors">
                About
              </Link>
              <Link href="/products" className="text-amber-800 hover:text-amber-600 font-medium transition-colors">
                Products
              </Link>
              <Link href="/facility" className="text-amber-800 hover:text-amber-600 font-medium transition-colors">
                Distillery
              </Link>
              <Link href="/tours" className="text-amber-800 hover:text-amber-600 font-medium transition-colors">
                Tours
              </Link>
              <Link href="/events" className="text-amber-800 hover:text-amber-600 font-medium transition-colors">
                Events
              </Link>
              <Link href="/contact" className="text-amber-800 hover:text-amber-600 font-medium transition-colors">
                Contact
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Coming Soon Content */}
      <div className="pt-20 flex items-center justify-center min-h-screen">
        <div className="text-center max-w-2xl mx-auto px-4">
          <div className="mb-8">
            <img 
              src="/lovable-uploads/cfc50220-4059-4960-a0ea-0b328b8473e8.png" 
              alt="Shangrila Distillery" 
              className="h-32 w-auto mx-auto mb-6 opacity-80"
            />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-amber-900 mb-4">
            {title}
          </h1>
          <h2 className="text-2xl md:text-3xl font-light text-amber-700 mb-6">
            Coming Soon
          </h2>
          <p className="text-lg text-amber-600 mb-8">
            {description || "We're crafting something special for you. Stay tuned for updates on this exciting new addition to our distillery experience."}
          </p>
          <div className="bg-white/70 backdrop-blur-sm rounded-lg p-6 mb-8">
            <p className="text-amber-800 font-medium">
              In the meantime, explore our current offerings and learn about our craft distilling process in the Himalayas.
            </p>
          </div>
          <Link 
            href="/" 
            className="inline-flex items-center space-x-2 bg-amber-700 text-white px-6 py-3 rounded-lg font-semibold hover:bg-amber-800 transition-colors shadow-lg"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Back to Home</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ComingSoon;
