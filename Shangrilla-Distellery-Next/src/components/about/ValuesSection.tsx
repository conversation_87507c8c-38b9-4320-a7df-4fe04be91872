
import { Award, Users, MapPin } from "lucide-react";

const ValuesSection = () => {
  const values = [
    {
      icon: Award,
      title: "Quality Excellence",
      description: "Uncompromising commitment to the highest standards"
    },
    {
      icon: Users,
      title: "Craftsmanship",
      description: "Traditional techniques combined with modern innovation"
    },
    {
      icon: MapPin,
      title: "Nepalese Heritage",
      description: "Proudly representing Nepal's rich cultural traditions"
    }
  ];

  return (
    <div className="mb-20">
      <h2 className="text-3xl font-playfair font-bold text-amber-100 mb-12 text-center">Our Values</h2>
      <div className="grid md:grid-cols-3 gap-8">
        {values.map((value) => (
          <div key={value.title} className="distillery-card p-6 text-center hover:scale-105 transition-all duration-300">
            <div className="w-16 h-16 bg-amber-400/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <value.icon className="h-8 w-8 text-amber-400" />
            </div>
            <h3 className="text-xl font-playfair font-semibold text-amber-100 mb-2">{value.title}</h3>
            <p className="distillery-text">{value.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ValuesSection;
